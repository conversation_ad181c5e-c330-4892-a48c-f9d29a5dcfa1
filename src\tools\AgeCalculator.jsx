import React, { useState } from 'react';
import SEO from '../components/SEO';

const AgeCalculator = () => {
  const [birthDate, setBirthDate] = useState('');
  const [age, setAge] = useState(null);
  const [error, setError] = useState('');

  const calculateAge = () => {
    if (!birthDate) {
      setError('Please select a birth date');
      setAge(null);
      return;
    }

    setError('');
    
    const today = new Date();
    const birthDateObj = new Date(birthDate);
    
    if (birthDateObj > today) {
      setError('Birth date cannot be in the future');
      setAge(null);
      return;
    }
    
    let ageYears = today.getFullYear() - birthDateObj.getFullYear();
    let ageMonths = today.getMonth() - birthDateObj.getMonth();
    let ageDays = today.getDate() - birthDateObj.getDate();
    
    if (ageDays < 0) {
      ageMonths--;
      // Get the last day of the previous month
      const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0).getDate();
      ageDays += lastDayOfLastMonth;
    }
    
    if (ageMonths < 0) {
      ageYears--;
      ageMonths += 12;
    }
    
    setAge({ years: ageYears, months: ageMonths, days: ageDays });
  };

  const handleDateChange = (e) => {
    setBirthDate(e.target.value);
    setError('');
    setAge(null);
  };

  return (
    <>
      <SEO 
        title="Age Calculator - Calculate Your Exact Age | ToollyHub"
        description="Calculate your exact age in years, months, and days. Our free age calculator helps you determine precise age differences and important life milestones. Simple, accurate, and easy to use."
        keywords="age calculator, birthday calculator, age difference calculator, life calculator, date calculator, age in years months days, age calculation tool"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Age Calculator</h2>
          
          <div className="mb-4">
            <label htmlFor="birthDate" className="block text-gray-700 font-medium mb-2">
              Select Your Birth Date
            </label>
            <input
              type="date"
              id="birthDate"
              value={birthDate}
              onChange={handleDateChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {error && (
            <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}
          
          <button
            onClick={calculateAge}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Calculate Age
          </button>
          
          {age && (
            <div className="mt-6 p-4 bg-gray-100 rounded-md">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Your Age is:</h3>
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="bg-blue-100 p-3 rounded-md">
                  <span className="block text-3xl font-bold text-blue-700">{age.years}</span>
                  <span className="text-gray-600">Years</span>
                </div>
                <div className="bg-blue-100 p-3 rounded-md">
                  <span className="block text-3xl font-bold text-blue-700">{age.months}</span>
                  <span className="text-gray-600">Months</span>
                </div>
                <div className="bg-blue-100 p-3 rounded-md">
                  <span className="block text-3xl font-bold text-blue-700">{age.days}</span>
                  <span className="text-gray-600">Days</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AgeCalculator;
