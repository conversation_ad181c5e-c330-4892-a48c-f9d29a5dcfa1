import React, { useState } from 'react';
import SEO from '../components/SEO';

const AgeCalculator = () => {
  const [birthDate, setBirthDate] = useState('');
  const [age, setAge] = useState(null);
  const [error, setError] = useState('');

  const calculateAge = () => {
    if (!birthDate) {
      setError('Please select a birth date');
      setAge(null);
      return;
    }

    setError('');

    const today = new Date();
    const birthDateObj = new Date(birthDate);

    if (birthDateObj > today) {
      setError('Birth date cannot be in the future');
      setAge(null);
      return;
    }

    let ageYears = today.getFullYear() - birthDateObj.getFullYear();
    let ageMonths = today.getMonth() - birthDateObj.getMonth();
    let ageDays = today.getDate() - birthDateObj.getDate();

    if (ageDays < 0) {
      ageMonths--;
      // Get the last day of the previous month
      const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0).getDate();
      ageDays += lastDayOfLastMonth;
    }

    if (ageMonths < 0) {
      ageYears--;
      ageMonths += 12;
    }

    // Calculate additional details
    const totalDays = Math.floor((today - birthDateObj) / (1000 * 60 * 60 * 24));
    const totalWeeks = Math.floor(totalDays / 7);
    const totalMonths = ageYears * 12 + ageMonths;
    const totalHours = totalDays * 24;
    const totalMinutes = totalHours * 60;

    setAge({
      years: ageYears,
      months: ageMonths,
      days: ageDays,
      totalDays,
      totalWeeks,
      totalMonths,
      totalHours,
      totalMinutes
    });
  };

  const handleDateChange = (e) => {
    setBirthDate(e.target.value);
    setError('');
    setAge(null);
  };

  return (
    <>
      <SEO
        title="Age Calculator - Calculate Your Exact Age | ToollyHub"
        description="Calculate your exact age in years, months, and days. Our free age calculator helps you determine precise age differences and important life milestones. Simple, accurate, and easy to use."
        keywords="age calculator, birthday calculator, age difference calculator, life calculator, date calculator, age in years months days, age calculation tool"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6">
            <div className="flex items-center justify-center space-x-3">
              <div className="bg-white bg-opacity-20 p-3 rounded-full">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">Age Calculator</h1>
                <p className="text-purple-100 mt-1">Calculate your exact age with precision</p>
              </div>
            </div>
          </div>

          <div className="p-8">
            <div className="mb-6">
              <label htmlFor="birthDate" className="block text-gray-700 font-semibold mb-3 text-lg">
                Select Your Birth Date
              </label>
              <input
                type="date"
                id="birthDate"
                value={birthDate}
                onChange={handleDateChange}
                className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 text-lg bg-gray-50 hover:bg-white"
              />
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl flex items-center space-x-2">
                <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
            )}

            <button
              onClick={calculateAge}
              className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white py-4 px-6 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 transform hover:scale-105 font-semibold text-lg shadow-lg"
            >
              Calculate My Age
            </button>

            {age && (
              <div className="mt-8 space-y-6">
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-2xl border border-purple-200">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">Your Age is:</h3>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-white p-4 rounded-xl shadow-md border border-purple-200 transform transition-all duration-200 hover:scale-105">
                      <span className="block text-4xl font-bold text-purple-600">{age.years}</span>
                      <span className="text-gray-600 font-medium">Years</span>
                    </div>
                    <div className="bg-white p-4 rounded-xl shadow-md border border-purple-200 transform transition-all duration-200 hover:scale-105">
                      <span className="block text-4xl font-bold text-purple-600">{age.months}</span>
                      <span className="text-gray-600 font-medium">Months</span>
                    </div>
                    <div className="bg-white p-4 rounded-xl shadow-md border border-purple-200 transform transition-all duration-200 hover:scale-105">
                      <span className="block text-4xl font-bold text-purple-600">{age.days}</span>
                      <span className="text-gray-600 font-medium">Days</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-2xl border border-gray-200">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">Additional Details</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="bg-white p-3 rounded-lg text-center border border-gray-200">
                      <span className="block text-2xl font-bold text-blue-600">{age.totalDays?.toLocaleString()}</span>
                      <span className="text-gray-600">Total Days</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg text-center border border-gray-200">
                      <span className="block text-2xl font-bold text-green-600">{age.totalWeeks?.toLocaleString()}</span>
                      <span className="text-gray-600">Total Weeks</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg text-center border border-gray-200">
                      <span className="block text-2xl font-bold text-orange-600">{age.totalMonths}</span>
                      <span className="text-gray-600">Total Months</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg text-center border border-gray-200">
                      <span className="block text-2xl font-bold text-red-600">{age.totalHours?.toLocaleString()}</span>
                      <span className="text-gray-600">Total Hours</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AgeCalculator;
