import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const GradientGenerator = () => {
  const [colors, setColors] = useState(['#4158D0', '#C850C0']);
  const [gradientType, setGradientType] = useState('linear');
  const [direction, setDirection] = useState('to right');
  const [cssCode, setCssCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [presets, setPresets] = useState([
    { name: 'Sunset', colors: ['#FF512F', '#F09819'], type: 'linear', direction: 'to right' },
    { name: 'Ocean', colors: ['#2193b0', '#6dd5ed'], type: 'linear', direction: 'to bottom' },
    { name: 'Purple Love', colors: ['#cc2b5e', '#753a88'], type: 'linear', direction: 'to right' },
    { name: 'Summer', colors: ['#22c1c3', '#fdbb2d'], type: 'linear', direction: '135deg' },
    { name: 'Sky', colors: ['#1488CC', '#2B32B2'], type: 'linear', direction: 'to top' },
    { name: 'Rainbow', colors: ['#00F5A0', '#00D9F5', '#9E00FF'], type: 'linear', direction: 'to right' },
    { name: 'Radial Sunset', colors: ['#EB3349', '#F45C43'], type: 'radial', direction: 'circle' },
    { name: 'Cosmic Fusion', colors: ['#ff00cc', '#333399'], type: 'linear', direction: '45deg' },
  ]);
  
  // Directions for linear gradients
  const linearDirections = [
    'to right',
    'to left',
    'to bottom',
    'to top',
    'to bottom right',
    'to bottom left',
    'to top right',
    'to top left',
    '45deg',
    '135deg',
    '225deg',
    '315deg'
  ];
  
  // Shapes for radial gradients
  const radialShapes = [
    'circle',
    'ellipse'
  ];
  
  // Update CSS code when any of the gradient properties change
  useEffect(() => {
    generateCssCode();
  }, [colors, gradientType, direction]);
  
  // Generate CSS code based on current settings
  const generateCssCode = () => {
    let colorStops = '';
    
    colors.forEach((color, index) => {
      colorStops += color;
      if (index < colors.length - 1) {
        colorStops += ', ';
      }
    });
    
    let cssValue = '';
    if (gradientType === 'linear') {
      cssValue = `linear-gradient(${direction}, ${colorStops})`;
    } else if (gradientType === 'radial') {
      cssValue = `radial-gradient(${direction}, ${colorStops})`;
    }
    
    setCssCode(`background-image: ${cssValue};`);
  };
  
  // Handle color change for a specific index
  const handleColorChange = (index, value) => {
    const newColors = [...colors];
    newColors[index] = value;
    setColors(newColors);
  };
  
  // Add a new color
  const addColor = () => {
    if (colors.length < 5) { // Limit to 5 colors for simplicity
      // Generate a random color
      const randomColor = '#' + Math.floor(Math.random()*16777215).toString(16);
      setColors([...colors, randomColor]);
    }
  };
  
  // Remove a color at specific index
  const removeColor = (index) => {
    if (colors.length > 2) { // Minimum 2 colors required for gradient
      const newColors = [...colors];
      newColors.splice(index, 1);
      setColors(newColors);
    }
  };
  
  // Copy CSS code to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(cssCode).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };
  
  // Apply a preset
  const applyPreset = (preset) => {
    setColors(preset.colors);
    setGradientType(preset.type);
    setDirection(preset.direction);
  };
  
  // Save current gradient as a preset
  const saveAsPreset = () => {
    const presetName = prompt('Enter a name for this preset:');
    if (presetName) {
      const newPreset = {
        name: presetName,
        colors: [...colors],
        type: gradientType,
        direction: direction
      };
      setPresets([...presets, newPreset]);
    }
  };

  return (
    <>
      <SEO
        title="CSS Gradient Generator - Create Beautiful Gradients"
        description="Create stunning CSS gradients for your website with our easy-to-use gradient generator. Choose colors, direction, and type to generate ready-to-use CSS code."
        keywords="gradient generator, CSS gradient, background gradient, web design tool, color gradient, linear gradient, radial gradient, gradient CSS code"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-6xl mx-auto">
          {/* Modern Header */}
          <div className="bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 rounded-2xl shadow-xl p-8 mb-8">
            <h1 className="text-4xl font-bold text-white text-center mb-2">CSS Gradient Generator</h1>
            <p className="text-purple-100 text-center text-lg">Create stunning gradients for your web projects</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Controls Section */}
            <div className="lg:col-span-1 bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6">
                <h2 className="text-xl font-bold text-white">Gradient Settings</h2>
                <p className="text-blue-100 mt-1">Customize your gradient</p>
              </div>
              <div className="p-6 space-y-6">

                {/* Gradient Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Gradient Type</label>
                  <div className="flex space-x-3">
                    <button
                      className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                        gradientType === 'linear'
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md transform scale-105'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => setGradientType('linear')}
                    >
                      Linear
                    </button>
                    <button
                      className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                        gradientType === 'radial'
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md transform scale-105'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => setGradientType('radial')}
                    >
                      Radial
                    </button>
                  </div>
                </div>

                {/* Direction/Shape */}
                <div>
                  <label htmlFor="direction" className="block text-sm font-medium text-gray-700 mb-3">
                    {gradientType === 'linear' ? 'Direction' : 'Shape'}
                  </label>
                  <select
                    id="direction"
                    value={direction}
                    onChange={(e) => setDirection(e.target.value)}
                    className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 shadow-sm"
                  >
                    {gradientType === 'linear' ? (
                      linearDirections.map((dir) => (
                        <option key={dir} value={dir}>
                          {dir}
                        </option>
                      ))
                    ) : (
                      radialShapes.map((shape) => (
                        <option key={shape} value={shape}>
                          {shape}
                        </option>
                      ))
                    )}
                  </select>
                </div>

                {/* Color Stops */}
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <label className="block text-sm font-medium text-gray-700">Colors</label>
                    {colors.length < 5 && (
                      <button
                        onClick={addColor}
                        className="px-3 py-1 text-sm bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm"
                      >
                        + Add Color
                      </button>
                    )}
                  </div>

                  <div className="space-y-3">
                    {colors.map((color, index) => (
                      <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <input
                          type="color"
                          value={color}
                          onChange={(e) => handleColorChange(index, e.target.value)}
                          className="w-12 h-12 rounded-lg border-2 border-white shadow-md cursor-pointer"
                        />
                        <input
                          type="text"
                          value={color}
                          onChange={(e) => handleColorChange(index, e.target.value)}
                          className="flex-1 px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 shadow-sm"
                        />
                        {colors.length > 2 && (
                          <button
                            onClick={() => removeColor(index)}
                            className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Save as Preset */}
                <button
                  onClick={saveAsPreset}
                  className="w-full px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold rounded-lg hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md"
                >
                  Save as Preset
                </button>
              </div>
            </div>

            {/* Preview and Code Section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Gradient Preview */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-800 mb-4">Live Preview</h3>
                  <div
                    className="h-64 rounded-xl shadow-inner border-4 border-white flex items-center justify-center relative overflow-hidden"
                    style={{ backgroundImage: gradientType === 'linear'
                      ? `linear-gradient(${direction}, ${colors.join(', ')})`
                      : `radial-gradient(${direction}, ${colors.join(', ')})`
                    }}
                  >
                    <div className="absolute inset-0 bg-black/5"></div>
                    <span className="relative px-6 py-3 bg-white/90 backdrop-blur-sm rounded-xl font-semibold text-gray-800 shadow-lg">
                      Preview
                    </span>
                  </div>
                </div>
              </div>

              {/* CSS Code */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 p-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-bold text-white">CSS Code</h3>
                    <button
                      onClick={copyToClipboard}
                      className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 shadow-md"
                    >
                      {copied ? '✓ Copied!' : '📋 Copy'}
                    </button>
                  </div>
                </div>
                <div className="p-6">
                  <div className="bg-gray-900 p-4 rounded-xl font-mono text-sm overflow-x-auto border border-gray-200">
                    <code className="text-green-400">{cssCode}</code>
                  </div>
                </div>
              </div>

              {/* Presets */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6">
                  <h3 className="text-xl font-bold text-white">Gradient Presets</h3>
                  <p className="text-indigo-100 mt-1">Click any preset to apply it instantly</p>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                    {presets.map((preset, index) => (
                      <div
                        key={index}
                        onClick={() => applyPreset(preset)}
                        className="group relative h-20 rounded-xl shadow-md cursor-pointer hover:shadow-lg transform transition-all duration-300 hover:scale-105 overflow-hidden border-2 border-white hover:border-purple-300"
                        style={{
                          backgroundImage: preset.type === 'linear'
                            ? `linear-gradient(${preset.direction}, ${preset.colors.join(', ')})`
                            : `radial-gradient(${preset.direction}, ${preset.colors.join(', ')})`
                        }}
                        title={preset.name}
                      >
                        <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-all duration-300"></div>
                        <div className="absolute bottom-2 left-2 right-2">
                          <span className="text-xs bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg text-gray-800 truncate block text-center font-medium shadow-sm">
                            {preset.name}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* SEO Content */}
          <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">Using CSS Gradients in Web Design</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Types of CSS Gradients</h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  CSS gradients allow you to display smooth transitions between two or more specified colors. They are a popular way to add depth,
                  visual interest, and modern aesthetics to websites without using image files.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-blue-500 mr-2">•</span>
                    <strong>Linear Gradients:</strong> Transition colors along a straight line
                  </li>
                  <li className="flex items-center">
                    <span className="text-purple-500 mr-2">•</span>
                    <strong>Radial Gradients:</strong> Transition colors outward from a central point
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-700 mb-4">Benefits of CSS Gradients</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-green-500 mr-2">•</span>
                    Better performance than image files
                  </li>
                  <li className="flex items-center">
                    <span className="text-orange-500 mr-2">•</span>
                    Perfect scalability without quality loss
                  </li>
                  <li className="flex items-center">
                    <span className="text-red-500 mr-2">•</span>
                    Easy customization and modification
                  </li>
                  <li className="flex items-center">
                    <span className="text-indigo-500 mr-2">•</span>
                    Reduced HTTP requests
                  </li>
                </ul>
              </div>
            </div>
        </div>
        </div>
      </div>
    </>
  );
};

export default GradientGenerator;
