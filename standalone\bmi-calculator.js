document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('bmiForm');
    const weightInput = document.getElementById('weight');
    const heightInput = document.getElementById('height');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateBMI();
    });

    function calculateBMI() {
        const weight = parseFloat(weightInput.value);
        const height = parseFloat(heightInput.value);
        
        if (!weight || !height) {
            showError('Please enter both weight and height');
            return;
        }

        if (weight <= 0 || height <= 0) {
            showError('Please enter valid values');
            return;
        }
        
        // Weight in kg, height in cm
        const bmiValue = (weight / Math.pow(height / 100, 2)).toFixed(2);
        const bmi = parseFloat(bmiValue);
        const category = getBMICategory(bmi);
        
        showResult(bmi, category);
    }

    function getBMICategory(bmi) {
        if (bmi < 18.5) return 'Underweight';
        if (bmi >= 18.5 && bmi < 25) return 'Normal weight';
        if (bmi >= 25 && bmi < 30) return 'Overweight';
        if (bmi >= 30 && bmi < 35) return 'Obesity Class I';
        if (bmi >= 35 && bmi < 40) return 'Obesity Class II';
        return 'Obesity Class III';
    }

    function getCategoryColor(category) {
        switch (category) {
            case 'Underweight': return '#3b82f6';
            case 'Normal weight': return '#10b981';
            case 'Overweight': return '#f59e0b';
            case 'Obesity Class I': return '#f97316';
            case 'Obesity Class II': return '#ef4444';
            case 'Obesity Class III': return '#dc2626';
            default: return '#374151';
        }
    }

    function showResult(bmi, category) {
        const color = getCategoryColor(category);
        const scalePosition = Math.min(Math.max((bmi - 15) / (45 - 15) * 100, 0), 100);
        
        resultDiv.innerHTML = `
            <div class="result-container">
                <h3>Your BMI Result:</h3>
                <div class="bmi-display">
                    <div class="bmi-number">${bmi}</div>
                    <div class="bmi-category" style="color: ${color}">${category}</div>
                </div>
                
                <div class="bmi-scale">
                    <div class="scale-labels">
                        <span>Underweight</span>
                        <span>Normal</span>
                        <span>Overweight</span>
                        <span>Obese</span>
                    </div>
                    <div class="scale-bar">
                        <div class="scale-indicator" style="left: ${scalePosition}%"></div>
                    </div>
                    <div class="scale-values">
                        <span>15</span>
                        <span>18.5</span>
                        <span>25</span>
                        <span>30</span>
                        <span>45</span>
                    </div>
                </div>
                
                <div class="bmi-categories">
                    <h4>BMI Categories</h4>
                    <div class="categories-grid">
                        <div class="category-item">
                            <span class="category-name" style="color: #3b82f6">Underweight</span>
                            <span class="category-range">&lt; 18.5</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name" style="color: #10b981">Normal weight</span>
                            <span class="category-range">18.5 - 24.9</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name" style="color: #f59e0b">Overweight</span>
                            <span class="category-range">25 - 29.9</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name" style="color: #f97316">Obesity Class I</span>
                            <span class="category-range">30 - 34.9</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name" style="color: #ef4444">Obesity Class II</span>
                            <span class="category-range">35 - 39.9</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name" style="color: #dc2626">Obesity Class III</span>
                            <span class="category-range">≥ 40</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function showError(message) {
        resultDiv.innerHTML = `
            <div class="error-message">
                <span class="error-icon">⚠️</span>
                ${message}
            </div>
        `;
    }
});
