body {
    background: #f7faff;
    font-family: 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
}

.container {
    background: #fff;
    max-width: 400px;
    margin: 60px auto;
    padding: 32px 24px;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 80, 200, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    color: #1565c0;
    margin-bottom: 24px;
}

form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

label {
    color: #333;
    font-weight: 500;
}

input[type="number"] {
    padding: 10px;
    border: 1px solid #b3c6e0;
    border-radius: 8px;
    font-size: 1rem;
    background: #f4f8ff;
    color: #222;
}

button {
    background: #1565c0;
    color: #fff;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
}

button:hover {
    background: #003c8f;
}

#result {
    margin-top: 24px;
    color: #1565c0;
    font-size: 1.1rem;
    font-weight: 500;
    min-height: 24px;
}
