* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    background: #fff;
    max-width: 700px;
    width: 100%;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
}

.header {
    background: linear-gradient(135deg, #10b981, #059669);
    padding: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    color: white;
}

.icon {
    background: rgba(255, 255, 255, 0.2);
    padding: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-text h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

.header-text p {
    margin: 4px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.form-container {
    padding: 32px;
}

.input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 24px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

label {
    color: #374151;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

input[type="number"] {
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1.1rem;
    background: #f9fafb;
    color: #374151;
    transition: all 0.2s ease;
}

input[type="number"]:hover {
    background: #fff;
    border-color: #d1d5db;
}

input[type="number"]:focus {
    outline: none;
    border-color: #10b981;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

button {
    width: 100%;
    background: linear-gradient(135deg, #10b981, #059669);
    color: #fff;
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: translateY(0);
}

button:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

#result {
    padding: 0 32px 32px 32px;
}

.result-container {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-radius: 16px;
    padding: 24px;
    border: 2px solid #bbf7d0;
}

.result-container h3 {
    text-align: center;
    color: #374151;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 20px 0;
}

.bmi-display {
    text-align: center;
    margin-bottom: 24px;
}

.bmi-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: #10b981;
    line-height: 1;
    display: block;
    margin-bottom: 8px;
}

.bmi-category {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 24px;
}

.bmi-scale {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.scale-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
}

.scale-bar {
    position: relative;
    height: 16px;
    background: linear-gradient(to right, #3b82f6, #10b981, #f59e0b, #ef4444);
    border-radius: 8px;
    margin-bottom: 8px;
}

.scale-indicator {
    position: absolute;
    top: -2px;
    width: 12px;
    height: 12px;
    background: white;
    border: 2px solid #374151;
    border-radius: 50%;
    transform: translateX(-50%);
}

.scale-values {
    display: flex;
    justify-content: space-between;
    font-size: 0.7rem;
    color: #9ca3af;
}

.bmi-categories {
    background: #f9fafb;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.bmi-categories h4 {
    text-align: center;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 16px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.category-item {
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e5e7eb;
}

.category-name {
    font-weight: 600;
}

.category-range {
    color: #6b7280;
    font-weight: 500;
}

.error-message {
    background: #fef2f2;
    border: 2px solid #fecaca;
    color: #dc2626;
    padding: 16px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.error-icon {
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .input-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .header {
        padding: 24px;
        flex-direction: column;
        text-align: center;
    }

    .header-text h1 {
        font-size: 1.5rem;
    }

    .form-container {
        padding: 24px;
    }

    .bmi-number {
        font-size: 2.5rem;
    }

    .scale-labels {
        font-size: 0.7rem;
    }
}
