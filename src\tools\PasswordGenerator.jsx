import React, { useState, useEffect } from 'react';
import SEO from '../components/SEO';

const PasswordGenerator = () => {
  const [password, setPassword] = useState('');
  const [strength, setStrength] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [length, setLength] = useState(12);
  const [options, setOptions] = useState({
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true
  });

  // Password strength checker
  useEffect(() => {
    if (!password) {
      setStrength(null);
      setSuggestions([]);
      return;
    }

    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      numbers: /[0-9]/.test(password),
      symbols: /[^A-Za-z0-9]/.test(password)
    };

    // Calculate strength
    const score = Object.values(checks).filter(Boolean).length;
    let strengthLevel;
    let strengthColor;
    let newSuggestions = [];

    if (score <= 2) {
      strengthLevel = 'Weak';
      strengthColor = 'red';
    } else if (score <= 4) {
      strengthLevel = 'Medium';
      strengthColor = 'yellow';
    } else {
      strengthLevel = 'Strong';
      strengthColor = 'green';
    }

    // Generate suggestions
    if (!checks.length) newSuggestions.push('Make your password at least 8 characters long');
    if (!checks.uppercase) newSuggestions.push('Add uppercase letters (A-Z)');
    if (!checks.lowercase) newSuggestions.push('Add lowercase letters (a-z)');
    if (!checks.numbers) newSuggestions.push('Add numbers (0-9)');
    if (!checks.symbols) newSuggestions.push('Add special characters (!@#$%^&*)');

    setStrength({ level: strengthLevel, color: strengthColor });
    setSuggestions(newSuggestions);
  }, [password]);

  // Generate random password
  const generatePassword = () => {
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const numberChars = '0123456789';
    const symbolChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    let chars = '';
    if (options.uppercase) chars += uppercaseChars;
    if (options.lowercase) chars += lowercaseChars;
    if (options.numbers) chars += numberChars;
    if (options.symbols) chars += symbolChars;

    if (chars === '') {
      setSuggestions(['Please select at least one character type']);
      return;
    }

    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // Ensure the generated password meets all selected criteria
    if (options.uppercase && !/[A-Z]/.test(result)) return generatePassword();
    if (options.lowercase && !/[a-z]/.test(result)) return generatePassword();
    if (options.numbers && !/[0-9]/.test(result)) return generatePassword();
    if (options.symbols && !/[^A-Za-z0-9]/.test(result)) return generatePassword();

    setGeneratedPassword(result);
    setPassword(result);
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here instead of alert
      alert('✅ Password copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy: ', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('✅ Password copied to clipboard!');
    }
  };

  return (
    <>
      <SEO 
        title="Password Generator & Strength Checker | ToollyHub"
        description="Generate strong, secure passwords and check password strength in real-time. Get instant feedback and suggestions to improve your password security. Free, easy-to-use password tool."
        keywords="password generator, password strength checker, secure password, random password generator, password security, password strength meter, password suggestions, password complexity"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6">
            <h2 className="text-2xl font-bold text-white">Password Generator</h2>
            <p className="text-blue-100 mt-1">Generate secure passwords & check strength</p>
          </div>

          <div className="p-6 space-y-6">

            {/* Password Strength Checker */}
            <div className="space-y-4">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Check Password Strength
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
                  placeholder="Enter or generate a password"
                />
                {password && (
                  <button
                    onClick={() => copyToClipboard(password)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm font-medium"
                  >
                    📋 Copy
                  </button>
                )}
              </div>

              {strength && (
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">Password Strength:</span>
                    <span className={`text-sm font-semibold ${
                      strength.color === 'red' ? 'text-red-600' :
                      strength.color === 'yellow' ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {strength.level}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-300 ${
                        strength.color === 'red' ? 'bg-red-500' :
                        strength.color === 'yellow' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{
                        width: strength.level === 'Weak' ? '33%' : strength.level === 'Medium' ? '66%' : '100%'
                      }}
                    ></div>
                  </div>
                </div>
              )}

              {suggestions.length > 0 && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm font-medium text-yellow-800 mb-2">💡 Suggestions to improve:</p>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-yellow-500 mr-2">•</span>
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Password Generator */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800">Generate New Password</h3>

              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700">
                  Password Length: {length} characters
                </label>
                <input
                  type="range"
                  min="8"
                  max="32"
                  value={length}
                  onChange={(e) => setLength(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>8</span>
                  <span>32</span>
                </div>
              </div>

              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 mb-3">Character Types</label>
                <div className="grid grid-cols-1 gap-3">
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.uppercase}
                      onChange={(e) => setOptions({...options, uppercase: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600 rounded"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700">Include Uppercase Letters</span>
                    <span className="ml-auto text-xs text-gray-500 font-mono">A-Z</span>
                  </label>
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.lowercase}
                      onChange={(e) => setOptions({...options, lowercase: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600 rounded"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700">Include Lowercase Letters</span>
                    <span className="ml-auto text-xs text-gray-500 font-mono">a-z</span>
                  </label>
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.numbers}
                      onChange={(e) => setOptions({...options, numbers: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600 rounded"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700">Include Numbers</span>
                    <span className="ml-auto text-xs text-gray-500 font-mono">0-9</span>
                  </label>
                  <label className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors cursor-pointer">
                    <input
                      type="checkbox"
                      checked={options.symbols}
                      onChange={(e) => setOptions({...options, symbols: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600 rounded"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700">Include Special Characters</span>
                    <span className="ml-auto text-xs text-gray-500 font-mono">!@#$%^&*</span>
                  </label>
                </div>
              </div>

              <button
                onClick={generatePassword}
                className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02]"
              >
                🔐 Generate Secure Password
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PasswordGenerator; 