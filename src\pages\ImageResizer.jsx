import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const ImageResizer = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 });
  const [resizeMethod, setResizeMethod] = useState('dimensions'); // 'dimensions' or 'percentage'
  const [newDimensions, setNewDimensions] = useState({ width: '', height: '' });
  const [percentage, setPercentage] = useState('');
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [resizedImageUrl, setResizedImageUrl] = useState(null);
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setOriginalDimensions({ width: img.width, height: img.height });
          setNewDimensions({ width: img.width.toString(), height: img.height.toString() });
          setImagePreview(e.target.result);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDimensionChange = (dimension, value) => {
    if (maintainAspectRatio && originalDimensions.width && originalDimensions.height) {
      const aspectRatio = originalDimensions.width / originalDimensions.height;
      
      if (dimension === 'width') {
        const newWidth = parseInt(value) || 0;
        const newHeight = Math.round(newWidth / aspectRatio);
        setNewDimensions({ width: value, height: newHeight.toString() });
      } else {
        const newHeight = parseInt(value) || 0;
        const newWidth = Math.round(newHeight * aspectRatio);
        setNewDimensions({ width: newWidth.toString(), height: value });
      }
    } else {
      setNewDimensions(prev => ({ ...prev, [dimension]: value }));
    }
  };

  const handlePercentageChange = (value) => {
    setPercentage(value);
    if (originalDimensions.width && originalDimensions.height && value) {
      const scale = parseFloat(value) / 100;
      const newWidth = Math.round(originalDimensions.width * scale);
      const newHeight = Math.round(originalDimensions.height * scale);
      setNewDimensions({ width: newWidth.toString(), height: newHeight.toString() });
    }
  };

  const resizeImage = () => {
    if (!selectedImage || !newDimensions.width || !newDimensions.height) return;

    setIsProcessing(true);
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      const targetWidth = parseInt(newDimensions.width);
      const targetHeight = parseInt(newDimensions.height);
      
      canvas.width = targetWidth;
      canvas.height = targetHeight;
      
      ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
      
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        setResizedImageUrl(url);
        setIsProcessing(false);
      }, selectedImage.type, 0.9);
    };
    
    img.src = imagePreview;
  };

  const downloadResizedImage = () => {
    if (!resizedImageUrl) return;
    
    const link = document.createElement('a');
    link.href = resizedImageUrl;
    link.download = `resized_${selectedImage.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setOriginalDimensions({ width: 0, height: 0 });
    setNewDimensions({ width: '', height: '' });
    setPercentage('');
    setResizedImageUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <SEO
        title="Image Resizer - Resize Images Online | ToollyHub"
        description="Resize images online by dimensions or percentage. Easy-to-use image resizer tool with aspect ratio preservation. Supports JPG, PNG, and other image formats."
        keywords="image resizer, resize image online, image dimensions, image scaling, photo resizer, image compression, aspect ratio, image editor"
      />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto p-6">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 mb-8">
              <h1 className="text-4xl font-bold text-white text-center mb-2">Image Resizer</h1>
              <p className="text-blue-100 text-center text-lg">Resize your images by dimensions or percentage</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Upload and Controls */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-green-500 to-teal-500 p-6">
                  <h2 className="text-xl font-bold text-white">Upload & Resize Settings</h2>
                  <p className="text-green-100 mt-1">Choose your image and resize options</p>
                </div>
                
                <div className="p-6 space-y-6">
                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="image-upload"
                      />
                      <label htmlFor="image-upload" className="cursor-pointer">
                        <div className="text-gray-600">
                          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                          <p className="text-lg font-medium">Click to upload an image</p>
                          <p className="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
                        </div>
                      </label>
                    </div>
                  </div>

                  {selectedImage && (
                    <>
                      {/* Original Dimensions */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-medium text-gray-700 mb-2">Original Dimensions</h3>
                        <p className="text-gray-600">{originalDimensions.width} × {originalDimensions.height} pixels</p>
                        <p className="text-sm text-gray-500">File: {selectedImage.name}</p>
                      </div>

                      {/* Resize Method */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">Resize Method</label>
                        <div className="flex space-x-3">
                          <button
                            className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                              resizeMethod === 'dimensions' 
                                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md' 
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                            onClick={() => setResizeMethod('dimensions')}
                          >
                            By Dimensions
                          </button>
                          <button
                            className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                              resizeMethod === 'percentage' 
                                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md' 
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                            onClick={() => setResizeMethod('percentage')}
                          >
                            By Percentage
                          </button>
                        </div>
                      </div>

                      {resizeMethod === 'dimensions' ? (
                        <div className="space-y-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="aspect-ratio"
                              checked={maintainAspectRatio}
                              onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <label htmlFor="aspect-ratio" className="text-sm text-gray-700">
                              Maintain aspect ratio
                            </label>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">Width (px)</label>
                              <input
                                type="number"
                                value={newDimensions.width}
                                onChange={(e) => handleDimensionChange('width', e.target.value)}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                min="1"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">Height (px)</label>
                              <input
                                type="number"
                                value={newDimensions.height}
                                onChange={(e) => handleDimensionChange('height', e.target.value)}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                min="1"
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Scale Percentage</label>
                          <input
                            type="number"
                            value={percentage}
                            onChange={(e) => handlePercentageChange(e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="e.g., 50 for 50%"
                            min="1"
                            max="500"
                          />
                          <p className="text-sm text-gray-500 mt-1">Enter percentage (1-500%)</p>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex space-x-3">
                        <button
                          onClick={resizeImage}
                          disabled={isProcessing || !newDimensions.width || !newDimensions.height}
                          className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-teal-500 text-white font-semibold rounded-lg hover:from-green-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isProcessing ? 'Processing...' : 'Resize Image'}
                        </button>
                        <button
                          onClick={resetTool}
                          className="px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
                        >
                          Reset
                        </button>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Preview Section */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-6">
                  <h2 className="text-xl font-bold text-white">Preview</h2>
                  <p className="text-purple-100 mt-1">Original and resized image preview</p>
                </div>
                
                <div className="p-6">
                  {imagePreview ? (
                    <div className="space-y-6">
                      {/* Original Image */}
                      <div>
                        <h3 className="font-medium text-gray-700 mb-3">Original Image</h3>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <img
                            src={imagePreview}
                            alt="Original"
                            className="max-w-full h-auto max-h-48 mx-auto rounded"
                          />
                        </div>
                      </div>

                      {/* Resized Image */}
                      {resizedImageUrl && (
                        <div>
                          <div className="flex justify-between items-center mb-3">
                            <h3 className="font-medium text-gray-700">Resized Image</h3>
                            <button
                              onClick={downloadResizedImage}
                              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 text-sm"
                            >
                              Download
                            </button>
                          </div>
                          <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                            <img
                              src={resizedImageUrl}
                              alt="Resized"
                              className="max-w-full h-auto max-h-48 mx-auto rounded"
                            />
                            <p className="text-center text-sm text-gray-600 mt-2">
                              {newDimensions.width} × {newDimensions.height} pixels
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-gray-500">Upload an image to see the preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Hidden Canvas for Processing */}
            <canvas ref={canvasRef} style={{ display: 'none' }} />

            {/* SEO Content */}
            <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-6">About Image Resizing</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">Why Resize Images?</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Image resizing is essential for web optimization, social media posts, email attachments, and storage management.
                    Our tool helps you quickly resize images while maintaining quality and aspect ratios.
                  </p>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <span className="text-blue-500 mr-2">•</span>
                      Reduce file size for faster loading
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">•</span>
                      Optimize for social media platforms
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-500 mr-2">•</span>
                      Prepare images for email attachments
                    </li>
                    <li className="flex items-center">
                      <span className="text-orange-500 mr-2">•</span>
                      Create thumbnails and previews
                    </li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">Features</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <span className="text-red-500 mr-2">•</span>
                      Resize by exact dimensions or percentage
                    </li>
                    <li className="flex items-center">
                      <span className="text-indigo-500 mr-2">•</span>
                      Maintain aspect ratio automatically
                    </li>
                    <li className="flex items-center">
                      <span className="text-teal-500 mr-2">•</span>
                      Support for JPG, PNG, GIF formats
                    </li>
                    <li className="flex items-center">
                      <span className="text-pink-500 mr-2">•</span>
                      Instant preview and download
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageResizer;
