* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    background: #fff;
    max-width: 600px;
    width: 100%;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.container:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
}

.header {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    padding: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    color: white;
}

.icon {
    background: rgba(255, 255, 255, 0.2);
    padding: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-text h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

.header-text p {
    margin: 4px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.form-container {
    padding: 32px;
}

.input-group {
    margin-bottom: 24px;
}

label {
    display: block;
    color: #374151;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

input[type="date"] {
    width: 100%;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1.1rem;
    background: #f9fafb;
    color: #374151;
    transition: all 0.2s ease;
}

input[type="date"]:hover {
    background: #fff;
    border-color: #d1d5db;
}

input[type="date"]:focus {
    outline: none;
    border-color: #8b5cf6;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

button {
    width: 100%;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    color: #fff;
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: translateY(0);
}

button:hover {
    background: linear-gradient(135deg, #7c3aed, #9333ea);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

#result {
    padding: 0 32px 32px 32px;
}

.result-container {
    background: linear-gradient(135deg, #f3e8ff, #ede9fe);
    border-radius: 16px;
    padding: 24px;
    border: 2px solid #e9d5ff;
}

.result-container h3 {
    text-align: center;
    color: #374151;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 20px 0;
}

.age-display {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.age-item {
    background: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
    border: 2px solid #e9d5ff;
    transition: transform 0.2s ease;
}

.age-item:hover {
    transform: translateY(-2px);
}

.age-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #8b5cf6;
    line-height: 1;
}

.age-label {
    color: #6b7280;
    font-weight: 600;
    font-size: 0.9rem;
}

.additional-details {
    background: #f9fafb;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e5e7eb;
}

.additional-details h4 {
    text-align: center;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 16px 0;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

.detail-item {
    background: white;
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.detail-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    line-height: 1;
}

.detail-label {
    color: #6b7280;
    font-weight: 500;
    font-size: 0.8rem;
}

.error-message {
    background: #fef2f2;
    border: 2px solid #fecaca;
    color: #dc2626;
    padding: 16px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.error-icon {
    font-size: 1.2rem;
}

@media (max-width: 640px) {
    .age-display {
        grid-template-columns: 1fr;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    .header {
        padding: 24px;
        flex-direction: column;
        text-align: center;
    }

    .header-text h1 {
        font-size: 1.5rem;
    }

    .form-container {
        padding: 24px;
    }
}
