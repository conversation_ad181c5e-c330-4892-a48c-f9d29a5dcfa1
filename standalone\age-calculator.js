document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('ageForm');
    const dobInput = document.getElementById('dob');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateAge();
    });

    function calculateAge() {
        const birthDate = dobInput.value;
        
        if (!birthDate) {
            showError('Please select a birth date');
            return;
        }

        const today = new Date();
        const birthDateObj = new Date(birthDate);
        
        if (birthDateObj > today) {
            showError('Birth date cannot be in the future');
            return;
        }
        
        let ageYears = today.getFullYear() - birthDateObj.getFullYear();
        let ageMonths = today.getMonth() - birthDateObj.getMonth();
        let ageDays = today.getDate() - birthDateObj.getDate();
        
        if (ageDays < 0) {
            ageMonths--;
            const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0).getDate();
            ageDays += lastDayOfLastMonth;
        }
        
        if (ageMonths < 0) {
            ageYears--;
            ageMonths += 12;
        }

        // Calculate additional details
        const totalDays = Math.floor((today - birthDateObj) / (1000 * 60 * 60 * 24));
        const totalWeeks = Math.floor(totalDays / 7);
        const totalMonths = ageYears * 12 + ageMonths;
        
        showResult({
            years: ageYears,
            months: ageMonths,
            days: ageDays,
            totalDays: totalDays,
            totalWeeks: totalWeeks,
            totalMonths: totalMonths
        });
    }

    function showResult(age) {
        resultDiv.innerHTML = `
            <div class="result-container">
                <h3>Your Age is:</h3>
                <div class="age-display">
                    <div class="age-item">
                        <span class="age-number">${age.years}</span>
                        <span class="age-label">Years</span>
                    </div>
                    <div class="age-item">
                        <span class="age-number">${age.months}</span>
                        <span class="age-label">Months</span>
                    </div>
                    <div class="age-item">
                        <span class="age-number">${age.days}</span>
                        <span class="age-label">Days</span>
                    </div>
                </div>
                <div class="additional-details">
                    <h4>Additional Details</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <span class="detail-number">${age.totalDays.toLocaleString()}</span>
                            <span class="detail-label">Total Days</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-number">${age.totalWeeks.toLocaleString()}</span>
                            <span class="detail-label">Total Weeks</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-number">${age.totalMonths}</span>
                            <span class="detail-label">Total Months</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function showError(message) {
        resultDiv.innerHTML = `
            <div class="error-message">
                <span class="error-icon">⚠️</span>
                ${message}
            </div>
        `;
    }
});
