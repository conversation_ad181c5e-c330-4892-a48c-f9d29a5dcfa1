import React, { useState } from 'react';
import SEO from '../components/SEO';

const BMICalculator = () => {
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [unit, setUnit] = useState('metric'); // 'metric' or 'imperial'
  const [bmi, setBmi] = useState(null);
  const [bmiCategory, setBmiCategory] = useState('');
  const [error, setError] = useState('');

  const calculateBMI = () => {
    if (!weight || !height) {
      setError('Please enter both weight and height');
      setBmi(null);
      setBmiCategory('');
      return;
    }

    if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
      setError('Please enter valid values');
      setBmi(null);
      setBmiCategory('');
      return;
    }

    setError('');
    
    let bmiValue;
    if (unit === 'metric') {
      // Weight in kg, height in cm
      bmiValue = (weight / Math.pow(height / 100, 2)).toFixed(2);
    } else {
      // Weight in lbs, height in inches
      bmiValue = ((weight * 703) / Math.pow(height, 2)).toFixed(2);
    }
    
    setBmi(parseFloat(bmiValue));
    setBmiCategory(getBMICategory(parseFloat(bmiValue)));
  };

  const getBMICategory = (bmi) => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi >= 18.5 && bmi < 25) return 'Normal weight';
    if (bmi >= 25 && bmi < 30) return 'Overweight';
    if (bmi >= 30 && bmi < 35) return 'Obesity Class I';
    if (bmi >= 35 && bmi < 40) return 'Obesity Class II';
    return 'Obesity Class III';
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Underweight': return 'text-blue-600';
      case 'Normal weight': return 'text-green-600';
      case 'Overweight': return 'text-yellow-600';
      case 'Obesity Class I': return 'text-orange-600';
      case 'Obesity Class II': return 'text-red-500';
      case 'Obesity Class III': return 'text-red-700';
      default: return 'text-gray-800';
    }
  };

  const handleUnitChange = (e) => {
    setUnit(e.target.value);
    setWeight('');
    setHeight('');
    setBmi(null);
    setBmiCategory('');
    setError('');
  };

  return (
    <>
      <SEO
        title="BMI Calculator - Calculate Your Body Mass Index | ToollyHub"
        description="Free BMI calculator to determine your Body Mass Index. Get instant BMI results, understand your weight category, and learn about healthy BMI ranges. Simple, accurate, and easy to use."
        keywords="BMI calculator, body mass index, weight calculator, health calculator, BMI formula, weight category, healthy weight, obesity calculator, underweight calculator, overweight calculator"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="bg-gradient-to-r from-green-500 to-green-600 p-6">
            <div className="flex items-center justify-center space-x-3">
              <div className="bg-white bg-opacity-20 p-3 rounded-full">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">BMI Calculator</h1>
                <p className="text-green-100 mt-1">Calculate your Body Mass Index</p>
              </div>
            </div>
          </div>

          <div className="p-8">
            <div className="mb-6">
              <label className="block text-gray-700 font-semibold mb-3 text-lg">Unit System</label>
              <div className="flex space-x-4 bg-gray-50 p-4 rounded-xl">
                <label className="inline-flex items-center cursor-pointer">
                  <input
                    type="radio"
                    value="metric"
                    checked={unit === 'metric'}
                    onChange={handleUnitChange}
                    className="form-radio h-5 w-5 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-3 text-gray-700 font-medium">Metric (kg, cm)</span>
                </label>
                <label className="inline-flex items-center cursor-pointer">
                  <input
                    type="radio"
                    value="imperial"
                    checked={unit === 'imperial'}
                    onChange={handleUnitChange}
                    className="form-radio h-5 w-5 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-3 text-gray-700 font-medium">Imperial (lbs, in)</span>
                </label>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="weight" className="block text-gray-700 font-semibold mb-3 text-lg">
                  Weight ({unit === 'metric' ? 'kg' : 'lbs'})
                </label>
                <input
                  type="number"
                  id="weight"
                  value={weight}
                  onChange={(e) => setWeight(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-lg bg-gray-50 hover:bg-white"
                  placeholder={`Enter weight in ${unit === 'metric' ? 'kilograms' : 'pounds'}`}
                />
              </div>

              <div>
                <label htmlFor="height" className="block text-gray-700 font-semibold mb-3 text-lg">
                  Height ({unit === 'metric' ? 'cm' : 'in'})
                </label>
                <input
                  type="number"
                  id="height"
                  value={height}
                  onChange={(e) => setHeight(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 text-lg bg-gray-50 hover:bg-white"
                  placeholder={`Enter height in ${unit === 'metric' ? 'centimeters' : 'inches'}`}
                />
              </div>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl flex items-center space-x-2">
                <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
            )}

            <button
              onClick={calculateBMI}
              className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 px-6 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 transform hover:scale-105 font-semibold text-lg shadow-lg"
            >
              Calculate My BMI
            </button>

            {bmi !== null && (
              <div className="mt-8 space-y-6">
                <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-2xl border border-green-200">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">Your BMI Result</h3>
                  <div className="text-center mb-4">
                    <p className="text-5xl font-bold text-green-600 mb-2">{bmi}</p>
                    <p className={`text-2xl font-semibold ${getCategoryColor(bmiCategory)}`}>
                      {bmiCategory}
                    </p>
                  </div>

                  {/* BMI Scale Visual */}
                  <div className="mt-6">
                    <div className="flex justify-between text-xs text-gray-600 mb-2">
                      <span>Underweight</span>
                      <span>Normal</span>
                      <span>Overweight</span>
                      <span>Obese</span>
                    </div>
                    <div className="relative h-4 bg-gradient-to-r from-blue-400 via-green-400 via-yellow-400 to-red-400 rounded-full">
                      <div
                        className="absolute top-0 w-3 h-3 bg-white border-2 border-gray-800 rounded-full transform -translate-y-0.5"
                        style={{
                          left: `${Math.min(Math.max((bmi - 15) / (45 - 15) * 100, 0), 100)}%`,
                          transform: 'translateX(-50%) translateY(-25%)'
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>15</span>
                      <span>18.5</span>
                      <span>25</span>
                      <span>30</span>
                      <span>45</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-2xl border border-gray-200">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">BMI Categories</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-lg border border-blue-200 flex justify-between items-center">
                      <span className="text-blue-600 font-medium">Underweight</span>
                      <span className="text-gray-600">&lt; 18.5</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-green-200 flex justify-between items-center">
                      <span className="text-green-600 font-medium">Normal weight</span>
                      <span className="text-gray-600">18.5 - 24.9</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-yellow-200 flex justify-between items-center">
                      <span className="text-yellow-600 font-medium">Overweight</span>
                      <span className="text-gray-600">25 - 29.9</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-orange-200 flex justify-between items-center">
                      <span className="text-orange-600 font-medium">Obesity Class I</span>
                      <span className="text-gray-600">30 - 34.9</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-red-200 flex justify-between items-center">
                      <span className="text-red-500 font-medium">Obesity Class II</span>
                      <span className="text-gray-600">35 - 39.9</span>
                    </div>
                    <div className="bg-white p-3 rounded-lg border border-red-300 flex justify-between items-center">
                      <span className="text-red-700 font-medium">Obesity Class III</span>
                      <span className="text-gray-600">≥ 40</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default BMICalculator;
