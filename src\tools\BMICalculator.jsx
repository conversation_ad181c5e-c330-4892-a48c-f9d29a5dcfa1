import React, { useState } from 'react';
import SEO from '../components/SEO';

const BMICalculator = () => {
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [unit, setUnit] = useState('metric'); // 'metric' or 'imperial'
  const [bmi, setBmi] = useState(null);
  const [bmiCategory, setBmiCategory] = useState('');
  const [error, setError] = useState('');

  const calculateBMI = () => {
    if (!weight || !height) {
      setError('Please enter both weight and height');
      setBmi(null);
      setBmiCategory('');
      return;
    }

    if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
      setError('Please enter valid values');
      setBmi(null);
      setBmiCategory('');
      return;
    }

    setError('');
    
    let bmiValue;
    if (unit === 'metric') {
      // Weight in kg, height in cm
      bmiValue = (weight / Math.pow(height / 100, 2)).toFixed(2);
    } else {
      // Weight in lbs, height in inches
      bmiValue = ((weight * 703) / Math.pow(height, 2)).toFixed(2);
    }
    
    setBmi(parseFloat(bmiValue));
    setBmiCategory(getBMICategory(parseFloat(bmiValue)));
  };

  const getBMICategory = (bmi) => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi >= 18.5 && bmi < 25) return 'Normal weight';
    if (bmi >= 25 && bmi < 30) return 'Overweight';
    if (bmi >= 30 && bmi < 35) return 'Obesity Class I';
    if (bmi >= 35 && bmi < 40) return 'Obesity Class II';
    return 'Obesity Class III';
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Underweight': return 'text-blue-600';
      case 'Normal weight': return 'text-green-600';
      case 'Overweight': return 'text-yellow-600';
      case 'Obesity Class I': return 'text-orange-600';
      case 'Obesity Class II': return 'text-red-500';
      case 'Obesity Class III': return 'text-red-700';
      default: return 'text-gray-800';
    }
  };

  const handleUnitChange = (e) => {
    setUnit(e.target.value);
    setWeight('');
    setHeight('');
    setBmi(null);
    setBmiCategory('');
    setError('');
  };

  return (
    <>
      <SEO 
        title="BMI Calculator - Calculate Your Body Mass Index | ToollyHub"
        description="Free BMI calculator to determine your Body Mass Index. Get instant BMI results, understand your weight category, and learn about healthy BMI ranges. Simple, accurate, and easy to use."
        keywords="BMI calculator, body mass index, weight calculator, health calculator, BMI formula, weight category, healthy weight, obesity calculator, underweight calculator, overweight calculator"
      />
      <div className="container mx-auto p-6">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">BMI Calculator</h2>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">Unit System</label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  value="metric"
                  checked={unit === 'metric'}
                  onChange={handleUnitChange}
                  className="form-radio h-4 w-4 text-blue-600"
                />
                <span className="ml-2">Metric (kg, cm)</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  value="imperial"
                  checked={unit === 'imperial'}
                  onChange={handleUnitChange}
                  className="form-radio h-4 w-4 text-blue-600"
                />
                <span className="ml-2">Imperial (lbs, in)</span>
              </label>
            </div>
          </div>
          
          <div className="mb-4">
            <label htmlFor="weight" className="block text-gray-700 font-medium mb-2">
              Weight ({unit === 'metric' ? 'kg' : 'lbs'})
            </label>
            <input
              type="number"
              id="weight"
              value={weight}
              onChange={(e) => setWeight(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={`Enter weight in ${unit === 'metric' ? 'kilograms' : 'pounds'}`}
            />
          </div>
          
          <div className="mb-4">
            <label htmlFor="height" className="block text-gray-700 font-medium mb-2">
              Height ({unit === 'metric' ? 'cm' : 'in'})
            </label>
            <input
              type="number"
              id="height"
              value={height}
              onChange={(e) => setHeight(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={`Enter height in ${unit === 'metric' ? 'centimeters' : 'inches'}`}
            />
          </div>
          
          {error && (
            <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}
          
          <button
            onClick={calculateBMI}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Calculate BMI
          </button>
          
          {bmi !== null && (
            <div className="mt-6 p-4 bg-gray-100 rounded-md">
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Your BMI Result:</h3>
              <p className="text-3xl font-bold text-center mb-2">{bmi}</p>
              <p className={`text-center text-lg font-medium ${getCategoryColor(bmiCategory)}`}>
                {bmiCategory}
              </p>
              <div className="mt-4 text-sm text-gray-600">
                <p className="mb-1"><span className="font-medium">BMI Categories:</span></p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><span className="text-blue-600 font-medium">Underweight:</span> &lt; 18.5</li>
                  <li><span className="text-green-600 font-medium">Normal weight:</span> 18.5 - 24.9</li>
                  <li><span className="text-yellow-600 font-medium">Overweight:</span> 25 - 29.9</li>
                  <li><span className="text-orange-600 font-medium">Obesity Class I:</span> 30 - 34.9</li>
                  <li><span className="text-red-500 font-medium">Obesity Class II:</span> 35 - 39.9</li>
                  <li><span className="text-red-700 font-medium">Obesity Class III:</span> ≥ 40</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default BMICalculator;
