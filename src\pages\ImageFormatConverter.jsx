import React, { useState, useRef } from 'react';
import SEO from '../components/SEO';

const ImageFormatConverter = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [originalFormat, setOriginalFormat] = useState('');
  const [targetFormat, setTargetFormat] = useState('jpeg');
  const [quality, setQuality] = useState(90);
  const [isProcessing, setIsProcessing] = useState(false);
  const [convertedImageUrl, setConvertedImageUrl] = useState(null);
  const [originalSize, setOriginalSize] = useState(0);
  const [convertedSize, setConvertedSize] = useState(0);
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  const supportedFormats = [
    { value: 'jpeg', label: 'JPEG', extension: '.jpg', description: 'Best for photos with many colors' },
    { value: 'png', label: 'PNG', extension: '.png', description: 'Best for images with transparency' },
    { value: 'webp', label: 'WebP', extension: '.webp', description: 'Modern format with excellent compression' },
    { value: 'gif', label: 'GIF', extension: '.gif', description: 'Best for simple animations' },
    { value: 'bmp', label: 'BMP', extension: '.bmp', description: 'Uncompressed bitmap format' },
  ];

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      setOriginalSize(file.size);
      
      // Detect original format
      const fileType = file.type.split('/')[1];
      setOriginalFormat(fileType === 'jpeg' ? 'jpg' : fileType);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const convertImage = () => {
    if (!selectedImage || !imagePreview) return;

    setIsProcessing(true);
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      // For GIF and PNG with transparency, handle background
      if (targetFormat === 'jpeg' || targetFormat === 'bmp') {
        // Fill with white background for formats that don't support transparency
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }
      
      ctx.drawImage(img, 0, 0);
      
      // Determine MIME type and quality
      let mimeType;
      let qualityValue = quality / 100;
      
      switch (targetFormat) {
        case 'jpeg':
          mimeType = 'image/jpeg';
          break;
        case 'png':
          mimeType = 'image/png';
          qualityValue = 1; // PNG doesn't use quality parameter
          break;
        case 'webp':
          mimeType = 'image/webp';
          break;
        case 'gif':
          mimeType = 'image/gif';
          qualityValue = 1; // GIF doesn't use quality parameter
          break;
        case 'bmp':
          mimeType = 'image/bmp';
          qualityValue = 1; // BMP doesn't use quality parameter
          break;
        default:
          mimeType = 'image/jpeg';
      }
      
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setConvertedImageUrl(url);
          setConvertedSize(blob.size);
        }
        setIsProcessing(false);
      }, mimeType, qualityValue);
    };
    
    img.src = imagePreview;
  };

  const downloadConvertedImage = () => {
    if (!convertedImageUrl) return;
    
    const format = supportedFormats.find(f => f.value === targetFormat);
    const link = document.createElement('a');
    link.href = convertedImageUrl;
    link.download = `converted_${selectedImage.name.split('.')[0]}${format.extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetTool = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setOriginalFormat('');
    setTargetFormat('jpeg');
    setQuality(90);
    setConvertedImageUrl(null);
    setOriginalSize(0);
    setConvertedSize(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getCompressionRatio = () => {
    if (originalSize === 0 || convertedSize === 0) return 0;
    return ((originalSize - convertedSize) / originalSize * 100).toFixed(1);
  };

  return (
    <>
      <SEO
        title="Image Format Converter - Convert Images Online | ToollyHub"
        description="Convert images between different formats including JPEG, PNG, WebP, GIF, and BMP. Free online image format converter with quality control."
        keywords="image converter, format converter, jpeg to png, png to webp, image format, convert images, webp converter, gif converter"
      />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto p-6">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-xl p-8 mb-8">
              <h1 className="text-4xl font-bold text-white text-center mb-2">Image Format Converter</h1>
              <p className="text-purple-100 text-center text-lg">Convert between JPEG, PNG, WebP, GIF, and BMP formats</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Upload and Controls */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6">
                  <h2 className="text-xl font-bold text-white">Upload & Convert Settings</h2>
                  <p className="text-indigo-100 mt-1">Choose your image and target format</p>
                </div>
                
                <div className="p-6 space-y-6">
                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Select Image</label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="image-upload"
                      />
                      <label htmlFor="image-upload" className="cursor-pointer">
                        <div className="text-gray-600">
                          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                          <p className="text-lg font-medium">Click to upload an image</p>
                          <p className="text-sm text-gray-500">JPEG, PNG, WebP, GIF, BMP supported</p>
                        </div>
                      </label>
                    </div>
                  </div>

                  {selectedImage && (
                    <>
                      {/* Original Format Info */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-medium text-gray-700 mb-2">Original Image</h3>
                        <p className="text-gray-600">Format: {originalFormat.toUpperCase()}</p>
                        <p className="text-gray-600">Size: {formatFileSize(originalSize)}</p>
                        <p className="text-sm text-gray-500">File: {selectedImage.name}</p>
                      </div>

                      {/* Target Format Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">Convert To</label>
                        <div className="grid grid-cols-1 gap-3">
                          {supportedFormats.map((format) => (
                            <div
                              key={format.value}
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                                targetFormat === format.value
                                  ? 'border-purple-500 bg-purple-50'
                                  : 'border-gray-200 hover:border-purple-300'
                              }`}
                              onClick={() => setTargetFormat(format.value)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <input
                                      type="radio"
                                      name="format"
                                      value={format.value}
                                      checked={targetFormat === format.value}
                                      onChange={() => setTargetFormat(format.value)}
                                      className="text-purple-600 focus:ring-purple-500"
                                    />
                                    <span className="font-medium text-gray-800">{format.label}</span>
                                    <span className="text-sm text-gray-500">{format.extension}</span>
                                  </div>
                                  <p className="text-sm text-gray-600 mt-1 ml-6">{format.description}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Quality Control for JPEG and WebP */}
                      {(targetFormat === 'jpeg' || targetFormat === 'webp') && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3">
                            Quality: {quality}%
                          </label>
                          <input
                            type="range"
                            min="10"
                            max="100"
                            value={quality}
                            onChange={(e) => setQuality(parseInt(e.target.value))}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                          />
                          <div className="flex justify-between text-sm text-gray-500 mt-1">
                            <span>Lower size</span>
                            <span>Higher quality</span>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex space-x-3">
                        <button
                          onClick={convertImage}
                          disabled={isProcessing}
                          className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transform transition-all duration-200 hover:scale-[1.02] shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isProcessing ? 'Converting...' : 'Convert Image'}
                        </button>
                        <button
                          onClick={resetTool}
                          className="px-6 py-3 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
                        >
                          Reset
                        </button>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Preview Section */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-green-500 to-teal-500 p-6">
                  <h2 className="text-xl font-bold text-white">Preview & Download</h2>
                  <p className="text-green-100 mt-1">Original and converted image comparison</p>
                </div>
                
                <div className="p-6">
                  {imagePreview ? (
                    <div className="space-y-6">
                      {/* Original Image */}
                      <div>
                        <h3 className="font-medium text-gray-700 mb-3">Original Image</h3>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <img
                            src={imagePreview}
                            alt="Original"
                            className="max-w-full h-auto max-h-48 mx-auto rounded"
                          />
                        </div>
                      </div>

                      {/* Converted Image */}
                      {convertedImageUrl && (
                        <div>
                          <div className="flex justify-between items-center mb-3">
                            <h3 className="font-medium text-gray-700">Converted Image</h3>
                            <button
                              onClick={downloadConvertedImage}
                              className="px-4 py-2 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-lg hover:from-green-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 text-sm"
                            >
                              📥 Download
                            </button>
                          </div>
                          <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                            <img
                              src={convertedImageUrl}
                              alt="Converted"
                              className="max-w-full h-auto max-h-48 mx-auto rounded"
                            />
                          </div>
                          
                          {/* Conversion Stats */}
                          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                            <h4 className="font-medium text-gray-700 mb-2">Conversion Results</h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-gray-600">Original:</span>
                                <p className="font-medium">{formatFileSize(originalSize)}</p>
                              </div>
                              <div>
                                <span className="text-gray-600">Converted:</span>
                                <p className="font-medium">{formatFileSize(convertedSize)}</p>
                              </div>
                              <div className="col-span-2">
                                <span className="text-gray-600">Size Change:</span>
                                <p className="font-medium">
                                  {convertedSize < originalSize ? (
                                    <span className="text-green-600">-{getCompressionRatio()}% smaller</span>
                                  ) : convertedSize > originalSize ? (
                                    <span className="text-red-600">+{Math.abs(getCompressionRatio())}% larger</span>
                                  ) : (
                                    <span className="text-gray-600">Same size</span>
                                  )}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-gray-500">Upload an image to see the preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Hidden Canvas for Processing */}
            <canvas ref={canvasRef} style={{ display: 'none' }} />

            {/* SEO Content */}
            <div className="mt-16 prose prose-lg max-w-none bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-6">About Image Format Conversion</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">Why Convert Image Formats?</h3>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    Different image formats serve different purposes. Converting between formats helps optimize images for web use,
                    reduce file sizes, ensure compatibility, and meet specific requirements for different platforms.
                  </p>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <span className="text-blue-500 mr-2">•</span>
                      Optimize images for web performance
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-500 mr-2">•</span>
                      Reduce file sizes for faster loading
                    </li>
                    <li className="flex items-center">
                      <span className="text-purple-500 mr-2">•</span>
                      Ensure compatibility across platforms
                    </li>
                    <li className="flex items-center">
                      <span className="text-orange-500 mr-2">•</span>
                      Meet specific format requirements
                    </li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-4">Supported Formats</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <span className="text-red-500 mr-2">•</span>
                      <strong>JPEG:</strong> Best for photos with many colors
                    </li>
                    <li className="flex items-center">
                      <span className="text-indigo-500 mr-2">•</span>
                      <strong>PNG:</strong> Perfect for images with transparency
                    </li>
                    <li className="flex items-center">
                      <span className="text-teal-500 mr-2">•</span>
                      <strong>WebP:</strong> Modern format with excellent compression
                    </li>
                    <li className="flex items-center">
                      <span className="text-pink-500 mr-2">•</span>
                      <strong>GIF:</strong> Ideal for simple animations
                    </li>
                    <li className="flex items-center">
                      <span className="text-yellow-500 mr-2">•</span>
                      <strong>BMP:</strong> Uncompressed bitmap format
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageFormatConverter;
